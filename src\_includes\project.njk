---
layout: "base.njk"
permalink: "/{{ lang }}/projects/{{ slug }}/"
---

<section>
  <div class="flex flex-col justify-center items-center h-full mx-auto max-md:text-center">
    <img
      class="h-[50vh] object-cover w-full"
      src="{{ featured_image }}"
      alt="{{ title }} "
    />
    <div
      class="flex max-md:flex-col min-h-[40vh] justify-center items-center h-full border-b border-black p-12"
    >
      <div class="flex-1 flex flex-col justify-center items-start p-8">
        <h1
          id="climate-action-title"
          class="text-4xl font-extrabold mb-4 highlight-effect"
        >
          <span class="highlight">{{ title }} </span>
        </h1>
        <p class="py-8">
          {{ summary }}
        </p>
      </div>

      <img
        class="flex-1 h-[40vh] max-w-[40vw] p-2 border border-black object-contain"
        src="{{ featured_image }}"
        alt="{{ title }} - Secondary"
      />
    </div>
  </div>
</section>
<section
  class="py-20 max-w-[90vw] text-justify mx-auto flex flex-col gap-8 justify-center items-center"
>
  <div class="prose prose-lg max-w-none">
    {{ content | safe }}
  </div>

  <img
    class="object-cover p-8 border border-black max-h-[80vh] m-auto min-w-[90vw]"
    src="{{ projectImage | default(featured_image) }}"
    alt="{{ title }} - Project Image"
  />

  {% if additionalContent %}
  <div class="flex max-md:gap-8 gap-[15vw] py-8 rich-text">
    <div class="prose prose-lg">
      <h2 class="text-3xl font-bold mb-4 highlight-effect">
        <span class="highlight">{{ ideation.additionalContent | default('Additional Insights') }}</span>
      </h2>
      <p>{{ additionalContent | safe }}</p>
    </div>
  </div>
  {% endif %}

  <img
    class="object-cover p-8 border border-black max-h-[80vh] m-auto min-w-[90vw]"
    src="{{ secondaryImage | default(featured_image) }}"
    alt="{{ title }} - Additional Image"
  />
</section>

{% if author %}
<div class="flex flex-col justify-center items-center h-screen py-12 gap-4">
  <div class="flex items-center gap-4">
    <div class="w-16 h-1 bg-[#60FAFF]"></div>
    <h1
      id="author-title"
      class="text-4xl font-bold mb-4 highlight-effect uppercase"
    >
      <span class="highlight">{{ author }}</span>
    </h1>
  </div>
  {% if quote %}
  <p
    id="quote-text"
    class="text-center p-8 text-gray-600 max-w-2xl highlight-effect"
  >
    <span
      class="text-justify text-2xl max-md:text-lg p-8 p-3 leading-[2.3em] text-black font-bold"
    >
      {{ quote }}
    </span>
  </p>
  {% endif %}
</div>

<section
  class="h-[80vh] w-[80vw] py-12 flex flex-col justify-center mx-auto items-center mb-16"
>
  <div class="flex justify-center items-center gap-9 max-md:flex-col py-12 h-[70vh]">
    <img
      class="flex-1 object-cover"
      src="{{ actionImage | default(featured_image) }}"
      width="300"
      height="300"
      alt="{{ title }} - Action Image"
    />
    <div class="flex flex-1 flex-col justify-center items-center gap-4">
      <div class="flex-1 p-8 ">
        <h1
          id="action-title"
          class="text-4xl font-bold mb-8 highlight-effect uppercase"
        >
          <span class="highlight">{{ project.action }}</span>
        </h1>
        <p class="">
          {{ actionContent | default(summary) }}
        </p>
      </div>
      <a
        href="{{ actionUrl | default('#') }}"
        class="self-end hover:scale-110 transition-transform duration-500"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="57.605"
          height="55.775"
          viewBox="0 0 57.605 55.775"
        >
          <path
            id="Polygon_3"
            data-name="Polygon 3"
            d="M22,0,44,39H0Z"
            transform="translate(0 22) rotate(-30)"
          />
        </svg>
      </a>
    </div>
  </div>
</section>
{% endif %}
