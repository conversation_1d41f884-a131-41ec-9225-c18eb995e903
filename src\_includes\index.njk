---
layout: "base.njk"
---

<!-- Main Content -->
<main class="pt-15 min-md:pb-12 px-4 max-w-[93vw] mx-auto">
  <video class="object-cover w-full max-lg:h-[50vh] " autoplay loop muted playsinline>
    <source
      src="/Assets/Videos/6804113-uhd_4096_2160_25fps.mp4"
      type="video/mp4"
    />
    <!-- Fallback image if video doesn't load -->
    <img
      class="object-cover h-screen w-full"
      src="/Assets/Images/pexels-picjumbo-com-55570-196646.jpg"
      alt="Header background"
    />
  </video>
  <!-- Hero Section -->
  <section
    class="h-[80vh] max-lg:h-[40vh] w-[80vw] flex flex-col justify-center mx-auto items-center mb-16"
  >
    <h1
      id="highlight-effect"
      class="text-5xl uppercase max-lg:leading-[2em] font-bold mb-6 text-gray-800"
    >
      <span class="highlight">{{ home.hero.title }}</span>
    </h1>
    <p class="text-xl text-gray-600 max-w-2xl">
      {{ home.hero.subtitle }}
    </p>
  </section>

  <!-- Services Grid -->
  <section class="grid lg:grid-cols-4 md:grid-cols-2 gap-8 ">
    <!-- Strategic Ideation -->
    <div
      id="strategic"
      class="border max-h-[80vh]  border-black p-8 hover:shadow-xl transition-all duration-500 max-md:text-center services-card"
    >
      <img
        class="object-contain h-[30vh] m-auto"
        src="/Assets/Images/11.png"
        alt="{{ home.services.strategic.title }}"
      />
      <h2 class="text-lg  font-bold mb-4 card-title">
        {{ home.services.strategic.title }}
      </h2>
      <p class="text-gray-600 mt-4 text-sm">
        {{ home.services.strategic.description }}
      </p>
    </div>

    <!-- Innovation Spirits -->
    <div
      id="innovation"
      class="border max-md:mt-8 max-h-[80vh] border-black p-8 hover:shadow-xl transition-all duration-500 max-md:text-center services-card"
    >
      <img
        class="object-contain h-[30vh] m-auto"
        src="/Assets/Images/4.png"
        alt="{{ home.services.innovation.title }}"
      />
      <h2 class="text-lg font-bold mb-4 card-title">
        {{ home.services.innovation.title }}
      </h2>
      <p class="text-gray-600 mt-4 text-sm">
        {{ home.services.innovation.description }}
      </p>
    </div>

    <!-- AI Augmented -->
    <div
      id="ai"
      class="border max-md:mt-8 max-h-[80vh] border-black p-8 hover:shadow-xl transition-all duration-500 max-md:text-center services-card"
    >
      <img
        class="object-contain h-[30vh] m-auto"
        src="/Assets/Images/8.png"
        alt="{{ home.services.ai.title }}"
      />
      <h2 class="text-lg font-bold mb-4 card-title">
        {{ home.services.ai.title }}
      </h2>
      <p class="text-gray-600 mt-4 text-sm">
        {{ home.services.ai.description }}
      </p>
    </div>

    <!-- Custom Platforms -->
    <div
      id="platforms"
      class="border max-md:mt-8 max-h-[80vh] border-black p-8 hover:shadow-xl transition-all duration-500 max-md:text-center services-card"
    >
      <img
        class="object-contain h-[30vh] m-auto"
        src="/Assets/Images/13.png"
        alt="{{ home.services.platforms.title }}"
      />
      <h2 class="text-lg font-bold mb-4 card-title">
        {{ home.services.platforms.title }}
      </h2>
      <p class="text-gray-600 mt-4 text-sm">
        {{ home.services.platforms.description }}
      </p>
    </div>
  </section>

  <!-- Dreamscaping Section -->
  <section
    class="py-20 w-full mx-auto flex justify-center items-center m-8 h-[80vh] bg-blue-200 bg-[url(/Assets/Images/Z-1.jpg)] bg-cover bg-blend-multiply bg-fixed"
  >
    <div
      class="flex flex-col justify-center items-center border w-[80vw] h-[70vh]"
    >
      <h1 class="text-6xl max-md:text-3xl  font-bold text-[#60FAFF] mb-8">
        {{ home.dreamscaping.title }}
      </h1>
      <p class="text-2xl max-md:p-8 text-white text-center italic max-w-3xl mx-auto leading-relaxed">
        {{ home.dreamscaping.description | replace('imagination', '<span
          class="text-black p-2 font-bold bg-[#60FAFF]"
          >imagination</span
        >') | safe }}
      </p>
    </div>
  </section>

  <!-- Who We Are Section -->
  <section
    class="py-20 max-md:text-center mt-20 w-full mx-auto flex justify-center items-center border border-black m-8 h-[80vh] max-md:h-full"
  >
    <div class="max-w-4xl mx-auto px-4 max-md:mt-12 max-md:flex-col flex justify-center items-center gap-11">
      <img
        class="mx-auto"
        width="350"
        src="/Assets/Images/2 (2).png"
        alt="Who we are"
      />

      <div class="">
        <h2
          id="highlight-effect-who"
          class="text-4xl font-bold text-gray-900 mb-10 highlight-effect"
        >
          <span class="highlight">{{ home.who.title }}</span>
        </h2>
        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
          {{ home.who.description }}
        </p>
        <a
          href={{ "/about/" | locale_url }}
          class="t ext-xl font-semibold border p-4 border-black"
        >
          {{ home.who.cta }}
        </a>
      </div>
    </div>
  </section>

  <!-- Our Mission Section -->
  <section
    class=" max-md:text-center py-20 mt-20 w-full mx-auto flex justify-center  items-center border border-black m-8 h-[80vh] max-md:h-full"
  >
    <div class="max-w-4xl mx-auto px-4 max-md:flex-col flex justify-center items-center gap-11">
      <div class="">
        <h2
          id="highlight-effect-mission"
          class="text-4xl font-bold mb-8 highlight-effect"
        >
          <span class="highlight">{{ home.mission.title }}</span>
        </h2>
        <p class="text-xl mb-8 leading-relaxed">
          {{ home.mission.description }}
        </p>
        <a
          href= {{ "/about/" | locale_url }}
          class="text-xl font-semibold border p-4 border-black"
        >
          {{ home.mission.cta }}
        </a>
      </div>
      <img
        class="mx-auto"
        width="430"
        src="/Assets/Images/الابتكار.png"
        alt="Our mission"
      />
    </div>
  </section>
</main>
