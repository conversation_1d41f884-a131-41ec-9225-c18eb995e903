@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

body{
  font-family: 'Tajawal', sans-serif;
}

/* RTL language support */
html[lang="ar"] {
  direction: rtl;
  text-align: right;
}

html[lang="en"] {
  direction: ltr;
  text-align: left;
}

/* Mobile Menu Styles */
#mobile-menu {
  transition: opacity 0.3s ease-in-out;
}

#mobile-menu.hidden {
  display: none;
}

#close-menu-btn {
  transition: background-color 0.2s ease;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .team-card {
    margin-bottom: 2rem;
  }

  .team-image-container {
    padding: 0 !important;
  }

  .team-image {
    height: 35vh !important;
  }

  /* Improve text readability on mobile */
  p, h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
  }

  /* Adjust font sizes for mobile */
  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem !important;
  }

  h3 {
    font-size: 1.25rem !important;
  }

  p {
    font-size: 1rem !important;
  }

  /* Improve spacing on mobile */
  .py-20 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .px-8 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Fix grid layout on mobile */
  .grid {
    grid-gap: 1rem !important;
  }

  /* Canvas container adjustments */
  #canvas_container {
    height: 40vh !important;
    max-height: 300px !important;
    overflow: hidden !important;
  }

  canvas {
    max-height: 300px !important;
  }

  /* Highlight effect adjustments for mobile */
  .highlight {
    padding: 2px !important;
    line-height: 1.8em !important;
  }

  /* Adjust section heights for mobile */
  section {
    min-height: auto !important;
    height: auto !important;
  }

  /* Mobile menu specific styles */
  #mobile-menu {
    background-color: rgba(255, 255, 255, 0.98);
  }

  #mobile-menu a {
    margin: 0.5rem 0;
    border-radius: 0.25rem;
    transition: background-color 0.2s ease;
  }

  #close-menu-btn {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  }
}

.highlight-container {
    overflow: visible;
    display: block;
  }

.highlight {
    position: relative;
    background-position: left;
    background-size: 0% 100%;
    background-image: linear-gradient(#60FAFF, #60FAFF);
    background-repeat: no-repeat;
    transition: background-size 0.8s ease, opacity 0.8s ease;
    padding: 4px;
    display: inline;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
    opacity: 0.3;
    will-change: background-size, opacity;
  }

  html[lang="ar"] .highlight {
    background-position: right;
  }

  .in-view.highlight, .highlight.in-view {
    background-size: 100% 100%;
    opacity: 1;
  }



  .card-title {
    background: linear-gradient(to right, rgba(255,255,255,0) 50%, #60FAFF 50%);
    background-size: 200%;
    background-position: 0 0;
    display: inline;
    transition: .5s ease-in-out;
    text-transform: uppercase;
    padding: 2px 4px;
  }

  .services-card {
    transition:all 0.5s ease;
  }

  .services-card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.304);
    transform: translateY(-10px) scale(1.02);
  }

  .services-card:hover .card-title {
    background-position: -100% 0;
  }

  .team-card {
    position: relative;
    transition: transform 0.5s ease;
    cursor: pointer;
  }

  .team-card:hover {
    transform: translateY(-10px);
  }

  .team-image-container {
    position: relative;
    overflow: hidden;
  }

  .team-image-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%);
    z-index: 0;
    transition: opacity 0.5s ease;
  }

  .team-card:hover .team-image-container::before {
    opacity: 0;
  }

  .team-image-container::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(96,250,255,0) 0%, rgba(96,250,255,1) 100%);
    z-index: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  .team-card:hover .team-image-container::after {
    opacity: 1;
  }

  .team-image {
    position: relative;
    z-index: 1;
    transition: transform 0.5s ease;
  }

  .team-card:hover .team-image {
    transform: scale(1.05);
  }

  .team-image-default {
    display: block;
    transition: opacity 0.5s ease;
  }

  .team-image-hover {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 1;
  }

  .team-card:hover .team-image-default {
    opacity: 0;
  }

  .team-card:hover .team-image-hover {
    opacity: 1;
  }

  .team-name {
    position: relative;
    display: inline-block;
  }

  .team-name-highlight {
    position: relative;
    background-position: left;
    background-size: 0% 100%;
    background-image: linear-gradient(#60FAFF, #60FAFF);
    background-repeat: no-repeat;
    transition: background-size 0.5s;
    padding: 4px;
  }

  .team-card:hover .team-name-highlight {
    background-size: 100% 100%;
  }

  footer{
  animation: sky 82s linear infinite;
}

@keyframes sky {
  50% {
    background-position: 600px 0px;
  }
  100% {
    background-position: 1200px 0px;
  }
}



      /* highlight animation underline */
      .highlight-bg {
        display: inline-block;
        background-image: linear-gradient(to right, #60faff, #60faff);
        background-repeat: no-repeat;
        background-size: 0% 0.4em;
        background-position: 0 100%;
        transition: background-size 0.4s ease-in-out;
      }
      .highlight-bg.is-visible {
        background-size: 100% 0.4em;
      }

      .highlight {
        position: relative;
        background-position: left;
        background-size: 0% 100%;
        background-image: linear-gradient(#60faff, #60faff);
        background-repeat: no-repeat;
        transition: background-size 0.8s ease, opacity 0.8s ease;
        padding: 4px;
        display: inline;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;
        opacity: 0.3;
        will-change: background-size, opacity;
      }

      html[lang="ar"] .highlight {
        background-position: right;
      }

      .in-view.highlight, .highlight.in-view {
        background-size: 100% 100%;
        opacity: 1;
      }

      /* 3D Space Animation Styles */
      #canvas_container {
        width: 100%;
        height: 100%;
        margin: 0 auto;
        position: relative;
        overflow: hidden;
        max-height: 600px;
      }

      canvas {
        display: block;
        max-height: 600px;
      }

      #fullscr {
        position: absolute;
        bottom: 5%;
        left: 50%;
        transform: translateX(-50%);
        border: 1px solid white;
        border-radius: 5px;
        font-size: 0.9rem;
        padding: 0.5rem 0.9em;
        background: #000000;
        color: white;
        -webkit-font-smoothing: antialiased;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s;
        z-index: 10;
      }

      #fullscr:hover {
        background: #ffffff;
        color: #000000;
      }



      /* highlight animation underline */
      .highlight-bg {
        display: inline-block;
        background-image: linear-gradient(to right, #60faff, #60faff);
        background-repeat: no-repeat;
        background-size: 0% 0.4em;
        background-position: 0 100%;
        transition: background-size 0.4s ease-in-out;
      }
      .highlight-bg.is-visible {
        background-size: 100% 0.4em;
      }
      /* arrow link styles */
      .arrow-link {
        display: inline-block;
        padding: 0.5rem;
        border: 2px solid #000;
        border-radius: 0.25rem;
        text-decoration: none;
        transform: rotate(45deg);
        width: 1rem;
        height: 1rem;
      }

      /* Highlight effect for text */
      .highlight {
        position: relative;
        background-position: left;
        background-size: 0% 100%;
        background-image: linear-gradient(#60FAFF, #60FAFF);
        background-repeat: no-repeat;
        transition: background-size 0.8s ease, opacity 0.8s ease;
        padding: 4px;
        display: inline;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;
        opacity: 0.3;
        will-change: background-size, opacity;
      }

      html[lang="ar"] .highlight {
        background-position: right;
      }

      .in-view.highlight, .highlight.in-view {
        background-size: 100% 100%;
        opacity: 1;
      }

      /* Contact form styles */
      .form-input {
        width: 100%;
        padding: 0.75rem;
        margin-bottom: 1rem;
        border: 2px solid #000;
        background-color: transparent;
      }
      input:lang(ar){
        direction: rtl;
      }
      select:lang(ar){
        background-image:none;
      }

      .form-input::placeholder {
        color: #6b7280;
      }

      .form-select {
        width: 100%;
        padding: 0.75rem;
        margin-bottom: 1rem;
        border: 2px solid #000;
        background-color: transparent;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23000'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 1rem;
      }

      .form-textarea {
        width: 100%;
        padding: 0.75rem;
        margin-bottom: 1rem;
        border: 2px solid #000;
        background-color: transparent;
        min-height: 150px;
      }

      .submit-btn {
        padding: 0.75rem 2rem;
        background-color: #000;
        color: white;
        font-weight: bold;
        border: 1px solid #000;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .submit-btn:hover {
        background-color: transparent;
        color: #000;
      }