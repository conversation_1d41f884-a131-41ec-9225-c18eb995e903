---
layout: "base.njk"
permalink: "/{{ lang }}/team/{{ slug }}/"
---

<section class="py-20 px-8 max-md:h-screen ">
  <div class="max-w-6xl mx-auto max-md:mt-12 mad-md:pt-12">
    <div class="flex flex-col md:flex-row gap-12">
      <!-- Team Member Image -->
      <div class="md:w-1/3">
        <div class="team-image-container">
          <img
            src="{{ image }}"
            alt="{{ name }}"
            class="team-image team-image-default h-auto w-full object-contain"
          />
          <img
            src="{{ image_hover }}"
            alt="{{ name }} Thinking"
            class="team-image team-image-hover h-auto w-full object-contain"
          />
        </div>
      </div>
      
      <!-- Team Member Info -->
      <div class="md:w-2/3">
        <h1 class="text-4xl font-bold mb-2">{{ name }}</h1>
        <h2 class="text-xl text-gray-600 mb-6">{{ position }}</h2>
        
        <div class="prose prose-lg max-w-none">
          <p>{{ bio }}</p>
        </div>
        
        <div class="mt-8">
          <a href="{{ '/team/' | locale_url }}" class="inline-block px-6 py-3 border border-gray-300 text-sm uppercase tracking-wide hover:bg-black hover:text-white transition-colors">
            {{ team.backToTeam }}
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .team-image-container {
    position: relative;
    overflow: hidden;
  }
  
  .team-image {
    transition: opacity 0.5s ease;
  }
  
  .team-image-default {
    opacity: 1;
  }
  
  .team-image-hover {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
  }
  
  .team-image-container:hover .team-image-default {
    opacity: 0;
  }
  
  .team-image-container:hover .team-image-hover {
    opacity: 1;
  }
</style>
