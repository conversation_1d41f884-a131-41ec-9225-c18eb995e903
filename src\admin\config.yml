backend:
  name: git-gateway
  branch: main

media_folder: "src/Assets/Images"
public_folder: "/Assets/Images"

collections:
  - name: "project"
    label: "Projects"
    folder: "src/en/project"
    create: true
    slug: "{{year}}-{{month}}-{{day}}-{{hour}}-{{minute}}-{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Date", name: "date", widget: "datetime", required: false }
      - { label: "Featured Image", name: "featured_image", widget: "image", required: false }
      - { label: "Project Image", name: "projectImage", widget: "image", required: false }
      - { label: "Secondary Image", name: "secondaryImage", widget: "image", required: false }
      - { label: "Action Image", name: "actionImage", widget: "image", required: false }
      - { label: "Company", name: "company", widget: "string", required: false }
      - { label: "Summary", name: "summary", widget: "text", required: false }
      - { label: "Additional Content", name: "additionalContent", widget: "text", required: false }
      - { label: "Action Content", name: "actionContent", widget: "text", required: false }
      - { label: "Action URL", name: "actionUrl", widget: "string", default: "/en/projects/", required: false }
      - { label: "Author", name: "author", widget: "string", required: false }
      - { label: "Quote", name: "quote", widget: "text", required: false }
      - { label: "Tags", name: "tags", widget: "list", default: ["project"], required: false }
      - { label: "Language", name: "lang", widget: "string", default: "en" }
      - { label: "Body", name: "body", widget: "markdown", required: false }

  - name: "project_ar"
    label: "Projects (Arabic)"
    folder: "src/ar/project"
    create: true
    slug: "{{year}}-{{month}}-{{day}}-{{hour}}-{{minute}}-{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Date", name: "date", widget: "datetime", required: false }
      - { label: "Featured Image", name: "featured_image", widget: "image", required: false }
      - { label: "Project Image", name: "projectImage", widget: "image", required: false }
      - { label: "Secondary Image", name: "secondaryImage", widget: "image", required: false }
      - { label: "Action Image", name: "actionImage", widget: "image", required: false }
      - { label: "Company", name: "company", widget: "string", required: false }
      - { label: "Summary", name: "summary", widget: "text", required: false }
      - { label: "Additional Content", name: "additionalContent", widget: "text", required: false }
      - { label: "Action Content", name: "actionContent", widget: "text", required: false }
      - { label: "Action URL", name: "actionUrl", widget: "string", default: "/ar/projects/", required: false }
      - { label: "Author", name: "author", widget: "string", required: false }
      - { label: "Quote", name: "quote", widget: "text", required: false }
      - { label: "Tags", name: "tags", widget: "list", default: ["project"], required: false }
      - { label: "Language", name: "lang", widget: "string", default: "ar" }
      - { label: "Body", name: "body", widget: "markdown", required: false }

  - name: "idea"
    label: "Ideas"
    folder: "src/en/idea"
    create: true
    slug: "{{year}}-{{month}}-{{day}}-{{hour}}-{{minute}}-{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Date", name: "date", widget: "datetime", required: false }
      - { label: "Featured Image", name: "featured_image", widget: "image", required: false }
      - { label: "Secondary Image", name: "secondaryImage", widget: "image", required: false }
      - { label: "Related Image", name: "relatedImage", widget: "image", required: false }
      - { label: "Reading Time (minutes)", name: "reading_time", widget: "number", required: false }
      - { label: "Tag", name: "tag", widget: "string", required: false }
      - { label: "Summary", name: "summary", widget: "text", required: false }
      - { label: "Additional Content", name: "additionalContent", widget: "text", required: false }
      - { label: "Related Content", name: "relatedContent", widget: "text", required: false }
      - { label: "Related URL", name: "relatedUrl", widget: "string", default: "/en/ideas/", required: false }
      - { label: "Author", name: "author", widget: "string", required: false }
      - { label: "Tags", name: "tags", widget: "list", default: ["idea"], required: false }
      - { label: "Language", name: "lang", widget: "string", default: "en" }
      - { label: "Body", name: "body", widget: "markdown", required: false }

  - name: "idea_ar"
    label: "Ideas (Arabic)"
    folder: "src/ar/idea"
    create: true
    slug: "{{year}}-{{month}}-{{day}}-{{hour}}-{{minute}}-{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Date", name: "date", widget: "datetime", required: false }
      - { label: "Featured Image", name: "featured_image", widget: "image", required: false }
      - { label: "Secondary Image", name: "secondaryImage", widget: "image", required: false }
      - { label: "Related Image", name: "relatedImage", widget: "image", required: false }
      - { label: "Reading Time (minutes)", name: "reading_time", widget: "number", required: false }
      - { label: "Tag", name: "tag", widget: "string", required: false }
      - { label: "Summary", name: "summary", widget: "text", required: false }
      - { label: "Additional Content", name: "additionalContent", widget: "text", required: false }
      - { label: "Related Content", name: "relatedContent", widget: "text", required: false }
      - { label: "Related URL", name: "relatedUrl", widget: "string", default: "/ar/ideas/", required: false }
      - { label: "Author", name: "author", widget: "string", required: false }
      - { label: "Tags", name: "tags", widget: "list", default: ["idea"], required: false }
      - { label: "Language", name: "lang", widget: "string", default: "ar" }
      - { label: "Body", name: "body", widget: "markdown", required: false }

  - name: "team"
    label: "Team Members"
    folder: "src/en/team"
    create: true
    slug: "{{slug}}"
    fields:
      - { label: "Name", name: "name", widget: "string" }
      - { label: "Position", name: "position", widget: "string", required: false }
      - { label: "Image", name: "image", widget: "image", required: false }
      - { label: "Image Hover", name: "image_hover", widget: "image", required: false }
      - { label: "Bio", name: "bio", widget: "text", required: false }
      - { label: "Tags", name: "tags", widget: "list", default: ["team-member"], required: false }
      - { label: "Language", name: "lang", widget: "string", default: "en" }

  - name: "team_ar"
    label: "Team Members (Arabic)"
    folder: "src/ar/team"
    create: true
    slug: "{{slug}}"
    fields:
      - { label: "Name", name: "name", widget: "string" }
      - { label: "Position", name: "position", widget: "string", required: false }
      - { label: "Image", name: "image", widget: "image", required: false }
      - { label: "Image Hover", name: "image_hover", widget: "image", required: false }
      - { label: "Bio", name: "bio", widget: "text", required: false }
      - { label: "Tags", name: "tags", widget: "list", default: ["team-member"], required: false }
      - { label: "Language", name: "lang", widget: "string", default: "ar" }