[build]
  command = "npm run build"
  publish = "public"

[dev]
  command = "npm run dev"
  port = 8080
  publish = "public"

[[redirects]]
  from = "/*"
  to = "/en/index.html"
  status = 200
  force = false
  conditions = {Language = ["en"]}

[[redirects]]
  from = "/*"
  to = "/ar/index.html"
  status = 200
  force = false
  conditions = {Language = ["ar"]}

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
