---
layout: "base.njk"
---

<section>
  <div class="flex flex-col justify-center items-center h-full mx-auto max-md:text-center">
    <img
      class="h-[60vh] max-md:h-[40vh] object-cover w-full"
      src="/Assets/Images/pexels-mikhail-nilov-9242849.jpg"
      alt="ideation image"
    />
    <div
      class="flex max-md:flex-col min-h-[40vh] justify-center items-center h-full border-b border-black py-8"
    >
      <h1
        class="flex-1 uppercase min-md:border-r border-black flex justify-center items-center text-8xl max-md:text-6xl font-extrabold mb-4"
      >
        {{ ideation.title }}
      </h1>
      <p class="flex-1 flex-col flex justify-center items-center p-8">
        <span class="font-bold"> {{ ideation.description }}</span>
        <br/>
        <br/>
        {{ ideation.description2 }}

    </div>
  </div>
</section>

<section class="py-20">
  <div class="mx-auto">
    <!-- Search and Filter Row -->
    <div
      class="flex flex-col sm:flex-row justify-center items-center mb-12 gap-4"
    >
      <div
        class="flex w-full sm:w-auto border border-gray-300 rounded max-md:w-[70vw]  px-8 py-2"
      >
        <input
          id="search-input"
          type="text"
          placeholder="{{ projects.search }}"
          class="w-full focus:outline-none bg-transparent "
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-400 ml-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <div class="relative">
        <button id="filter-button" class="border border-gray-300 px-4 py-2 text-sm">
          {{ projects.filter }}
        </button>
        <div id="filter-dropdown" class="absolute z-10 mt-1 w-48 bg-white border border-gray-300 rounded shadow-lg hidden">
          <div class="p-2">
            <a href="#" data-filter="all" class="block px-4 py-2 hover:bg-gray-100 bg-black text-white">{{ ideation.allCategories }}</a>
            <a href="#" data-filter="ai" class="block px-4 py-2 hover:bg-gray-100">AI</a>
            <a href="#" data-filter="innovation" class="block px-4 py-2 hover:bg-gray-100">{{ ideation.innovation }}</a>
            <a href="#" data-filter="strategy" class="block px-4 py-2 hover:bg-gray-100">{{ ideation.strategy }}</a>
          </div>
        </div>
      </div>

      <!-- Date Range Filter -->
      <div class="flex items-center gap-2">
        <div class="border border-gray-300 px-2 py-2 rounded">
          <label for="date-range-start" class="text-sm text-gray-600">{{ ideation.dateFrom }}</label>
          <input type="date" id="date-range-start" class="focus:outline-none bg-transparent ml-1">
        </div>
        <div class="border border-gray-300 px-2 py-2 rounded">
          <label for="date-range-end" class="text-sm text-gray-600">{{ ideation.dateTo }}</label>
          <input type="date" id="date-range-end" class="focus:outline-none bg-transparent ml-1">
        </div>
      </div>
    </div>

    <!-- Grid of Ideas -->
    <div id="content-grid" class="grid grid-cols-1 flex sm:grid-cols-2 lg:grid-cols-3 border border-black">
      {% for idea in collections["idea"] %}
      {% if idea.data.lang == page.lang %}
      <a href="{{ idea.url }}" class="border-r border-black transition-transform duration-500 ">
      <div class="content-item ">
        <div class="flex justify-between items-center p-4">
          <span class="item-tag uppercase bg-black text-white p-2" >
              {{ idea.data.tag }}
          </span>
          <span  class="uppercase">{{ ideation.readMore }}</span>
        </div>
        <img
          src="{{ idea.data.featured_image }}"
          alt="{{ idea.data.title }}"
          class="w-full h-56 object-cover"
        />
        <div class="p-4 flex flex-col">
          <h1 class="item-title text-3xl font-bold p-2 w-auto uppercase mb-1">
            {{ idea.data.title }}
          </h1>
          <p class="item-summary text-sm text-gray-600 p-3">
            {{ idea.data.summary | truncate(100) }}
          </p>
          <div class="flex justify-between">
            <h4 class="text-sm font-bold mb-2 uppercase m-2">
              {{ idea.data.author }}
            </h4>
            <h1 class="item-date" data-date="{{ idea.data.date }}">{{ idea.data.date | postDate }}</h1>
          </div>
        </div>
      </div>
    </a>
      {% endif %}
      {% endfor %}


    </div>

    <!-- Empty state when no results found -->
    <div id="empty-state" class="hidden py-12 text-center">
      <p class="text-xl text-gray-500">{{ ideation.noResults }}</p>
    </div>

    <!-- View More Button -->
    <div class="mt-12 flex justify-center">
      <button
        id="view-more-button"
        class="px-6 py-3 border border-gray-300 text-sm uppercase tracking-wide"
      >
        {{ projects.viewMore }}
      </button>
    </div>
  </div>
</section>
