<!-- Navbar -->
<nav class="bg-[#EFF5F5] fixed w-full z-50">
  <div class="max-w-[93vw] mx-auto px-4 max-md:flex  justify-between items-center">
    <div class="flex flex-row-reverse justify-between items-center h-16">
      <div class="flex items-center">
        <a href="/">
          <img
            src="/Assets/Images/Logo.png"
            alt="{{ site.title }}"
            class="h-12"
          />
        </a>
      </div>

      <!-- Desktop Menu -->
      <div class="hidden md:flex gap-8">
        <!-- Language Switcher -->
<div class="flex items-center space-x-2">
  {% if page.lang == "en" %}
    <a href="{{ page.url }}" class="text-[#60FAFF] font-bold">EN</a>
    <span class="text-gray-400">|</span>
    <a href="{{ page.url | replace('/en/', '/ar/') }}" class="text-gray-700 hover:text-gray-900 font-bold">AR</a>
  {% else %}
    <a href="{{ page.url | replace('/ar/', '/en/') }}" class="text-gray-700 hover:text-gray-900 font-bold">EN</a>
    <span class="text-gray-400">|</span>
    <a href="{{ page.url }}" class="text-[#60FAFF] font-bold">AR</a>
  {% endif %}
</div>



        <a href={{"/" | locale_url }} class=" text-gray-700 hover:text-gray-900">{{ nav.home }}</a>
        <a href={{"/projects/" | locale_url }} class="text-gray-700 hover:text-gray-900">{{ nav.projects }}</a>
        <a href={{"/ideation/" | locale_url }} class="text-gray-700 hover:text-gray-900">{{ nav.ideation }}</a>
        <a href={{"/about/" | locale_url }} class="text-gray-700 hover:text-gray-900">{{ nav.about }}</a>
        <a href={{"/team/" | locale_url }} class="text-gray-700 hover:text-gray-900">{{ nav.team }}</a>
        <a href={{"/contact/" | locale_url }} class="text-gray-700 hover:text-gray-900">{{ nav.contact }}</a>


        </div>
      </div>

      <!-- Mobile Menu Button -->
      <button id="menu-btn" class="md:hidden p-2 text-gray-600">
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          />
        </svg>
      </button>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="hidden fixed top-0 left-0 w-full h-full bg-white z-50 md:hidden overflow-y-auto transition-all duration-300 ease-in-out opacity-0">
    <div class="px-4 py-8 space-y-4 flex flex-col items-center text-center relative">
      <!-- Close button - positioned absolutely in the top-right corner -->
      <button id="close-menu-btn" class="absolute top-2 right-4 p-3 text-gray-600 hover:bg-gray-100 rounded-full">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      <!-- Logo at the top of mobile menu -->
      <div class="mb-8 mt-4">
        <a href="/">
          <img src="/Assets/Images/Logo.png" alt="{{ site.title }}" class="h-16 mx-auto" />
        </a>
      </div>

      <a href={{"/" | locale_url }} class="block w-full py-4 text-gray-700 hover:bg-gray-100 text-xl font-medium text-center">{{ nav.home }}</a>
      <a href={{"/projects/" | locale_url }} class="block w-full py-4 text-gray-700 hover:bg-gray-100 text-xl font-medium text-center">{{ nav.projects }}</a>
      <a href={{"/ideation/" | locale_url }} class="block w-full py-4 text-gray-700 hover:bg-gray-100 text-xl font-medium text-center">{{ nav.ideation }}</a>
      <a href={{"/about/" | locale_url }} class="block w-full py-4 text-gray-700 hover:bg-gray-100 text-xl font-medium text-center">{{ nav.about }}</a>
      <a href={{"/team/" | locale_url }} class="block w-full py-4 text-gray-700 hover:bg-gray-100 text-xl font-medium text-center">{{ nav.team }}</a>
      <a href={{"/contact/" | locale_url }} class="block w-full py-4 text-gray-700 hover:bg-gray-100 text-xl font-medium text-center">{{ nav.contact }}</a>

      <!-- Language Switcher -->
      <div class="flex items-center justify-center space-x-4 py-4 mt-4 w-full">
        {% if page.lang == "en" %}
          <a href="{{ page.url }}" class="text-[#60FAFF] hover:bg-gray-100 font-bold text-xl px-3 py-1">EN</a>
          <span class="text-gray-400">|</span>
          <a href="{{ page.url | replace('/en/', '/ar/') }}" class="text-gray-700 hover:bg-gray-100 font-bold text-xl px-3 py-1">AR</a>
        {% else %}
          <a href="{{ page.url | replace('/ar/', '/en/') }}" class="text-gray-700 hover:bg-gray-100 font-bold text-xl px-3 py-1">EN</a>
          <span class="text-gray-400">|</span>
          <a href="{{ page.url }}" class="text-[#60FAFF] hover:bg-gray-100 font-bold text-xl px-3 py-1">AR</a>
        {% endif %}
      </div>

     
    </div>
  </div>
</nav>