---
layout: "base.njk"
---

<!-- SECTION 1: Fullscreen Header with Typewriter Subheading -->
<header
  class="h-screen flex flex-col justify-center max-md:mx-auto items-center overflow-hidden"
>
  <h1 class="text-8xl text-center max-md:text-5xl  font-extrabold mb-4">{{ team.title }}</h1>
  <h2 id="subtitle" class="text-2xl font-medium"></h2>
</header>

<!-- SECTION 2: Quote -->
<section
  class="h-auto min-h-[80vh] w-[80vw] flex flex-col justify-center mx-auto items-center mb-16"
>
  <div class="text-xl text-gray-600 max-w-5xl text-justify " id="highlight-effect-team">
    <p class="mb-4 highlight-container">
      <span class="highlight text-xl p-3 leading-[2.3em] text-black font-bold" data-index="0">
        {{ team.quote1 }}
      </span>
    </p>
    <p class="mb-4 highlight-container">
      <span class="highlight text-xl p-3 leading-[2.3em] text-black font-bold" data-index="1">
        {{ team.quote2 }}
      </span>
    </p>
    <p class="mb-4 highlight-container">
      <span class="highlight text-xl p-3 leading-[2.3em] text-black font-bold" data-index="2">
        {{ team.quote3 }}
      </span>
    </p>
  </div>
</section>

<!-- SECTION 3: Team Grid Section -->
<section class="py-20 px-8">
  <div class="max-w-6xl mx-auto">
    {% if team.members and team.members.length > 0 %}
      <!-- Grid of teams -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {% for member in team.members %}
          <div class="team-card">
            <a href="/{{ lang }}/team/{{ member.name | arabicSlug }}/" class="block hover:scale-105 transition-transform duration-500">
              <div class="team-image-container px-12 mb-8">
                <img
                  src="{{ member.image or '/Assets/Images/rTkJ7v-man-png-image.png' }}"
                  alt="{{ member.name }}"
                  class="team-image team-image-default h-[45vh] mx-auto object-contain"
                />
                <img
                  src="{{ member.image_hover or '/Assets/Images/purepng.com-thinking-manthinking-manpersongentle-men-thinkingthinking-brain-1421526976458gpxqy.png' }}"
                  alt="{{ member.name }} Thinking"
                  class="team-image team-image-hover h-[45vh] mx-auto object-contain"
                />
              </div>
              <div class="p-8 flex flex-col border border-black">
                <h1 class="team-name text-3xl font-bold p-2 w-auto uppercase mb-1">
                  <span class="team-name-highlight">{{ member.name }}</span>
                </h1>
                {% if member.position %}
                <h4 class="text-sm font-bold mb-2 uppercase m-2">
                  {{ member.position }}
                </h4>
                {% endif %}
                <hr class="" />
                {% if member.bio %}
                <p class="text-sm text-gray-600 p-3">
                  {{ member.bio }}
                </p>
                {% endif %}
              </div>
            </a>
          </div>
        {% endfor %}
      </div>
    {% else %}
      <!-- Empty state when no team members -->
      <div class="text-center py-16">
        <p class="text-xl text-gray-500">
          {% if lang == "en" %}No team members to display at the moment.{% else %}لا يوجد أعضاء فريق للعرض في الوقت الحالي.{% endif %}
        </p>
      </div>
    {% endif %}
  </div>
</section>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    // ===== Typewriter on subtitle =====
    const messages = [
      "{{ team.subtitle['1'] }}",
      "{{ team.subtitle['2'] }}",
      "{{ team.subtitle['3'] }}"
    ];
    const speed = 100;
    const pause = 2000;
    let idx = 0,
      ch = 0,
      forward = true;
    const sub = document.getElementById("subtitle");
    function typeSub() {
      const msg = messages[idx];
      if (forward) {
        sub.textContent = msg.slice(0, ++ch);
        if (ch === msg.length) {
          forward = false;
          setTimeout(typeSub, pause);
        } else setTimeout(typeSub, speed);
      } else {
        sub.textContent = msg.slice(0, --ch);
        if (ch === 0) {
          forward = true;
          idx = (idx + 1) % messages.length;
          setTimeout(typeSub, speed);
        } else setTimeout(typeSub, speed / 2);
      }
    }
    typeSub();
  });
</script>

<style>
  .team-image-container {
    position: relative;
    overflow: hidden;
  }

  .team-image {
    transition: opacity 0.5s ease;
  }

  .team-image-default {
    opacity: 1;
  }

  .team-image-hover {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
  }

  .team-image-container:hover .team-image-default {
    opacity: 0;
  }

  .team-image-container:hover .team-image-hover {
    opacity: 1;
  }
</style>
