document.addEventListener("DOMContentLoaded", function() {
  // Elements
  const searchInput = document.getElementById('search-input');
  const filterButton = document.getElementById('filter-button');
  const filterDropdown = document.getElementById('filter-dropdown');
  const viewMoreButton = document.getElementById('view-more-button');
  const contentGrid = document.getElementById('content-grid');
  const contentItems = document.querySelectorAll('.content-item');
  const dateRangeStart = document.getElementById('date-range-start');
  const dateRangeEnd = document.getElementById('date-range-end');

  // Variables
  let visibleItems = 6; // Initial number of items to show
  let increment = 3; // Number of items to add when "View More" is clicked
  let currentFilter = 'all';
  let searchTerm = '';
  let startDate = null;
  let endDate = null;

  // Initialize from URL parameters
  initFromUrlParams();

  // Initialize items
  initializeItems();

  // Search functionality
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      searchTerm = this.value.toLowerCase();
      filterItems();
      updateUrlParams();
    });
  }

  // Filter button click
  if (filterButton) {
    filterButton.addEventListener('click', function() {
      if (filterDropdown) {
        filterDropdown.classList.toggle('hidden');
      }
    });
  }

  // Filter dropdown options
  if (filterDropdown) {
    const filterOptions = filterDropdown.querySelectorAll('a');
    filterOptions.forEach(option => {
      option.addEventListener('click', function(e) {
        e.preventDefault();
        currentFilter = this.getAttribute('data-filter');

        // Update active filter styling
        filterOptions.forEach(opt => opt.classList.remove('bg-black', 'text-white'));
        this.classList.add('bg-black', 'text-white');

        // Hide dropdown
        filterDropdown.classList.add('hidden');

        // Update filter button text
        if (filterButton) {
          filterButton.textContent = this.textContent;
        }

        filterItems();
        updateUrlParams();
      });
    });
  }

  // Date range filters
  if (dateRangeStart) {
    dateRangeStart.addEventListener('change', function() {
      startDate = this.value ? new Date(this.value) : null;
      filterItems();
      updateUrlParams();
    });
  }

  if (dateRangeEnd) {
    dateRangeEnd.addEventListener('change', function() {
      endDate = this.value ? new Date(this.value) : null;
      filterItems();
      updateUrlParams();
    });
  }

  // View More button
  if (viewMoreButton) {
    viewMoreButton.addEventListener('click', function() {
      visibleItems += increment;
      updateVisibility();
      updateUrlParams();

      // Hide button if all items are visible
      if (getFilteredItems().length <= visibleItems) {
        this.classList.add('hidden');
      }
    });
  }

  // Initialize from URL parameters
  function initFromUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);

    // Get search term
    if (urlParams.has('search') && searchInput) {
      searchTerm = urlParams.get('search').toLowerCase();
      searchInput.value = urlParams.get('search');
    }

    // Get filter
    if (urlParams.has('filter') && filterDropdown) {
      currentFilter = urlParams.get('filter');

      // Update active filter styling
      const filterOptions = filterDropdown.querySelectorAll('a');
      filterOptions.forEach(option => {
        if (option.getAttribute('data-filter') === currentFilter) {
          option.classList.add('bg-black', 'text-white');

          // Update filter button text
          if (filterButton) {
            filterButton.textContent = option.textContent;
          }
        } else {
          option.classList.remove('bg-black', 'text-white');
        }
      });
    }

    // Get date range
    if (urlParams.has('start') && dateRangeStart) {
      startDate = new Date(urlParams.get('start'));
      dateRangeStart.value = urlParams.get('start');
    }

    if (urlParams.has('end') && dateRangeEnd) {
      endDate = new Date(urlParams.get('end'));
      dateRangeEnd.value = urlParams.get('end');
    }

    // Get visible items count
    if (urlParams.has('count')) {
      visibleItems = parseInt(urlParams.get('count'));
    }
  }

  // Update URL parameters
  function updateUrlParams() {
    const urlParams = new URLSearchParams();

    // Add search term
    if (searchTerm) {
      urlParams.set('search', searchTerm);
    }

    // Add filter
    if (currentFilter !== 'all') {
      urlParams.set('filter', currentFilter);
    }

    // Add date range
    if (startDate) {
      urlParams.set('start', dateRangeStart.value);
    }

    if (endDate) {
      urlParams.set('end', dateRangeEnd.value);
    }

    // Add visible items count
    if (visibleItems > 6) {
      urlParams.set('count', visibleItems.toString());
    }

    // Update URL without reloading the page
    const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
    window.history.pushState({ path: newUrl }, '', newUrl);
  }

  // Initialize items
  function initializeItems() {
    if (!contentItems.length) return;

    // If there are fewer items than the initial visible count, hide the View More button
    if (contentItems.length <= visibleItems && viewMoreButton) {
      viewMoreButton.classList.add('hidden');
    }

    // Hide items beyond initial count
    updateVisibility();
  }

  // Filter items based on search and filter
  function filterItems() {
    visibleItems = 6; // Reset to initial count when filtering
    updateVisibility();

    // Show/hide View More button based on filtered results
    if (viewMoreButton) {
      if (getFilteredItems().length <= visibleItems) {
        viewMoreButton.classList.add('hidden');
      } else {
        viewMoreButton.classList.remove('hidden');
      }
    }
  }

  // Get filtered items based on current search, filter, and date range
  function getFilteredItems() {
    return Array.from(contentItems).filter(item => {
      const title = item.querySelector('.item-title')?.textContent.toLowerCase() || '';
      const summary = item.querySelector('.item-summary')?.textContent.toLowerCase() || '';
      const tag = item.querySelector('.item-tag')?.textContent.toLowerCase() || '';
      const dateElement = item.querySelector('.item-date');

      // Check if matches search term
      const matchesSearch = title.includes(searchTerm) || summary.includes(searchTerm);

      // Check if matches filter
      const matchesFilter = currentFilter === 'all' || tag.includes(currentFilter.toLowerCase());

      // Check if matches date range
      let matchesDateRange = true;
      if (dateElement && (startDate || endDate)) {
        const dateStr = dateElement.getAttribute('data-date') || dateElement.textContent;
        const itemDate = new Date(dateStr);

        if (startDate && itemDate < startDate) {
          matchesDateRange = false;
        }

        if (endDate && itemDate > endDate) {
          matchesDateRange = false;
        }
      }

      return matchesSearch && matchesFilter && matchesDateRange;
    });
  }

  // Update visibility of items
  function updateVisibility() {
    const filteredItems = getFilteredItems();

    // Hide all items first
    contentItems.forEach(item => {
      item.classList.add('hidden');
    });

    // Show filtered items up to visible count
    filteredItems.slice(0, visibleItems).forEach(item => {
      item.classList.remove('hidden');
    });

    // Show empty state if no results
    const emptyState = document.getElementById('empty-state');
    if (emptyState) {
      if (filteredItems.length === 0) {
        emptyState.classList.remove('hidden');
      } else {
        emptyState.classList.add('hidden');
      }
    }

    // Handle View More button visibility
    if (viewMoreButton) {
      // If there's only one item or fewer items than the visible count, hide the button
      if (filteredItems.length <= 1 || filteredItems.length <= visibleItems) {
        viewMoreButton.classList.add('hidden');
      } else {
        viewMoreButton.classList.remove('hidden');
      }
    }
  }
});
