---
layout: "base.njk"
---
<section class="py-24 min-h-screen max-w-[90vw] text-justify max-md:flex-col mx-auto flex gap-8 justify-center items-center">
    <div class="flex-1 flex flex-col max-md:items-center max-md:text-center">
        <h1 id="highlight-effect-contact" class="text-4xl font-bold mb-8"><span class="highlight">{{ contact.title }}</span></h1>
        <p class="text-xl mb-8">{{ contact.subtitle }}</p>
        <form name="contact" method="POST" data-netlify="true" data-netlify-recaptcha="true" class="max-w-2xl">
            <!-- First row: First and Last Name -->
            <div class="flex gap-4 mb-4">
                <input name="first name" type="text" placeholder="{{ contact.form.firstName }}" class="form-input flex-1" required>
                <input name="last name" type="text" placeholder="{{ contact.form.lastName }}" class="form-input flex-1" required>
            </div>

            <!-- Second row: Phone and Email -->
            <div class="flex gap-4 mb-4">
                <input name="tel" type="tel" placeholder="{{ contact.form.phone }}" class="form-input flex-1" required>
                <input name="email" type="email" placeholder="{{ contact.form.email }}" class="form-input flex-1" required>
            </div>

            <!-- Third row: How can we help you dropdown -->
            <div class="mb-4">
                <select name="help[]" class="form-select" required>
                    <option value="" disabled selected>{{ contact.form.help }}</option>
                    <option value="consulting">{{ contact.form.options.consulting }}</option>
                    <option value="innovation">{{ contact.form.options.innovation }}</option>
                    <option value="development">{{ contact.form.options.development }}</option>
                    <option value="other">{{ contact.form.options.other }}</option>
                </select>
            </div>

            <!-- Fourth row: Message textarea -->
            <div class="mb-4">
                <textarea name="message" placeholder="{{ contact.form.message }}" class="form-textarea" required></textarea>
            </div>
              <div data-netlify-recaptcha="true"></div>

            <!-- Fifth row: Submit button -->
            <div class="mb-4 max-md:mx-auto">
                <button type="submit" class="submit-btn">{{ contact.form.send }}</button>
            </div>
        </form>
    </div>
    <img class="object-contain" class="flex-1" width=450" height="550" src="/Assets/Images/الابداع.png" alt="{{ contact.title }}">
</section>

<style>
    .form-input, .form-select, .form-textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #000;
        background-color: transparent;
    }

    .form-textarea {
        min-height: 150px;
    }

    .submit-btn {
        background-color: #000;
        color: white;
        padding: 0.75rem 2rem;
        border: none;
        cursor: pointer;
        font-weight: bold;
    }

    .submit-btn:hover {
        background-color: #333;
    }
</style>
