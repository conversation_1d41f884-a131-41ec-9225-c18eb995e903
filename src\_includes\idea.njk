---
layout: "base.njk"
permalink: "/{{ lang }}/idea/{{ slug }}/"
---

<section>
  <div class="flex flex-col justify-center items-center h-full mx-auto max-md:text-center">
    <img
      class="h-[60vh] object-cover w-full"
      src="{{ featured_image }}"
      alt="{{ title }} "
    />
    <div
      class="flex flex-col min-h-[40vh] justify-center items-center w-full border-b border-black py-8"
    >
      <h1
        id="idea-title"
        class="flex-1 uppercase flex justify-center text-center items-center max-w-[60vw] max-md:text-5xl text-5xl font-extrabold mb-4 highlight-effect"
      >
        <span class="highlight">{{ title }}</span>
      </h1>
      <p class="flex-1 flex justify-center items-center p-8">
        {{ date | readableDate }}
      </p>
      <p class="uppercase bg-black text-white p-2">
        {{ tag }}
      </p>
    </div>
  </div>
</section>
<section
  class="py-20 max-w-[90vw] text-justify mx-auto flex flex-col gap-8 justify-center items-center"
>
  <div class="flex max-md:flex-col w-full p-8 justify-between items-center py-12 gap-4">
    <p class="uppercase">{{ ideation.readMin }}: <span class="text-black p-2 bg-[#60FAFF]">{{ reading_time }} : {{ common.minutes }}</span></p>
    <p class="uppercase bg-black text-white p-2">{{ ideation.author }} : {{ author }}</p>
  </div>
  <div class="prose prose-lg max-w-none">
    {{ content | safe }}
  </div>


  <img
    class="object-cover max-h-[80vh] m-auto min-w-[90vw]"
    src="{{ secondaryImage | default(featured_image) }}"
    alt="{{ title }} - Additional Image"
  />
  <div class="flex max-md:gap-8 gap-[15vw] py-8 rich-text">
    <div class="prose prose-lg">
      <h2 class="text-3xl font-bold mb-4 highlight-effect">
        <span class="highlight">{{ ideation.additionalContent | default('Additional Insights') }}</span>
      </h2>
      <p>{{ additionalContent | safe }}</p>
    </div>
  </div>

</section>

{% if relatedContent %}
<section class="py-12 max-w-[90vw] mx-auto">
  <div class="flex justify-center items-center gap-9 max-md:flex-col py-12 h-[70vh]">
    <img
      class="flex-1 object-cover"
      src="{{ relatedImage | default(featured_image) }}"
      width="300"
      height="300"
      alt="{{ title }} - Link Image"
    />
    <div class="flex flex-1 flex-col justify-center items-center gap-4">
      <div class="flex-1 p-8 ">
        <h1
          id="link-title"
          class="text-4xl font-bold mb-8 highlight-effect uppercase"
        >
          <span class="highlight">{{ ideation.linkText }}</span>
        </h1>
        <p class="">
          {{ relatedContent | safe }}
        </p>
      </div>
      <a
        href="{{ relatedUrl | default('#') }}"
        class="self-end hover:scale-110 transition-transform duration-500"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="57.605"
          height="55.775"
          viewBox="0 0 57.605 55.775"
        >
          <path
            id="Polygon_3"
            data-name="Polygon 3"
            d="M22,0,44,39H0Z"
            transform="translate(0 22) rotate(-30)"
          />
        </svg>
      </a>
    </div>
  </div>
</section>
{% endif %}
